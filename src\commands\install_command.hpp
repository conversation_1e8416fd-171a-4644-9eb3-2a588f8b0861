﻿#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/install_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <filesystem>
#include "../utils/output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <sddl.h>
#else
#include <unistd.h>
#endif

namespace sco {

class InstallCommand : public BaseCommand {
public:
    InstallCommand() = default;
    
    int execute() override {
        try {
            if (apps_.empty()) {
                output::error("<app> missing");
                show_usage();
                return 1;
            }

            // Check admin rights for global installation
            if (global_ && !is_admin()) {
                output::error("you need admin rights to install global apps");
                return 1;
            }

            // Step 1: Check if SCO/buckets are outdated and update if needed
            // (Following Scoop's behavior: update first, then check installation)
            if (!no_update_scoop_ && is_sco_outdated()) {
                std::cout << "SCO is out of date. Updating...\n";
                if (!update_sco_and_buckets()) {
                    output::warn("Failed to update SCO/buckets, continuing with installation");
                }
            }

            // Step 2: Clean up any previous failed installations
            ensure_none_failed();

            // Step 3: Check if apps are already installed (Scoop behavior)
            if (!check_already_installed()) {
                return 0;
            }

            // Show Scoop-style startup messages
            show_startup_messages();

            // Prepare install options
            InstallManager::InstallOptions options;
            options.global = global_;
            options.skip_dependencies = skip_dependencies_;
            options.force_reinstall = force_;
            options.no_cache = no_cache_;
            options.architecture = architecture_;

            // Show what we're about to install
            show_install_summary();

            // Perform installation
            auto start_time = std::chrono::steady_clock::now();
            auto result = InstallManager::install_apps(apps_, options);
            auto end_time = std::chrono::steady_clock::now();

            // Show results
            show_install_results(result);

            return result.success ? 0 : 1;

        } catch (const std::exception& e) {
            output::error("Install command failed: {}", e.what());
            std::cerr << "Installation failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "install"; }
    std::string get_description() const override { return "Install apps"; }
    
    // Setters for command options
    void set_apps(const std::vector<std::string>& apps) { apps_ = apps; }
    void set_global(bool global) { global_ = global; }
    void set_skip_dependencies(bool skip) { skip_dependencies_ = skip; }
    void set_force(bool force) { force_ = force; }
    void set_no_cache(bool no_cache) { no_cache_ = no_cache; }
    void set_no_update_scoop(bool no_update) { no_update_scoop_ = no_update; }
    void set_architecture(const std::string& arch) { architecture_ = arch; }
    
private:
    std::vector<std::string> apps_;
    bool global_ = false;
    bool skip_dependencies_ = false;
    bool force_ = false;
    bool no_cache_ = false;
    bool no_update_scoop_ = false;  // --no-update-scoop flag
    std::string architecture_ = "64bit";

    void show_usage() {
        std::cout << "Usage: sco install <app> [options]\n";
        std::cout << "e.g. The usual way to install an app (uses your local 'buckets'):\n";
        std::cout << "     sco install git\n\n";
        std::cout << "To install a different version of the app\n";
        std::cout << "(note that this will auto-generate the manifest using current version):\n";
        std::cout << "     sco install gh@2.7.0\n\n";
        std::cout << "To install an app from a manifest at a URL:\n";
        std::cout << "     sco install https://raw.githubusercontent.com/ScoopInstaller/Main/master/bucket/runat.json\n\n";
        std::cout << "Options:\n";
        std::cout << "  -g, --global                    Install the app globally\n";
        std::cout << "  -i, --independent               Don't install dependencies automatically\n";
        std::cout << "  -k, --no-cache                  Don't use the download cache\n";
        std::cout << "  -s, --skip-hash-check           Skip hash validation (use with caution!)\n";
        std::cout << "  -u, --no-update-scoop           Don't update Scoop before installing if it's outdated\n";
        std::cout << "  -a, --arch <32bit|64bit|arm64>  Use the specified architecture, if the app supports it\n";
    }

    bool is_admin() const {
        // Check if running with administrator privileges
        #ifdef _WIN32
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                   DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }
        return isAdmin == TRUE;
        #else
        return getuid() == 0;
        #endif
    }

    bool check_already_installed() {
        // Check if single app is already installed (Scoop behavior)
        if (apps_.size() == 1) {
            const auto& app_name = apps_[0];
            auto& config = Config::instance();
            auto app_dir = global_ ? config.get_global_apps_dir() / app_name
                                  : config.get_apps_dir() / app_name;

            if (std::filesystem::exists(app_dir)) {
                // Get current version
                auto current_dir = app_dir / "current";
                if (std::filesystem::exists(current_dir)) {
                    std::cout << "WARN  '" << app_name << "' is already installed.\n";
                    std::cout << "Use 'sco update " << app_name;
                    if (global_) std::cout << " --global";
                    std::cout << "' to install a new version.\n";
                    return false;
                }
            }
        }
        return true;
    }

    bool is_sco_outdated() const {
        // TODO: Implement SCO version checking
        // For now, assume SCO is not outdated
        return false;
    }

    bool update_sco_and_buckets() {
        // TODO: Implement SCO and bucket updating
        // This should update SCO itself and all configured buckets
        std::cout << "Updating SCO and buckets...\n";
        return true;
    }

    void ensure_none_failed() {
        // Scoop's ensure_none_failed function
        // Clean up any previous failed installations
        for (const auto& app_name : apps_) {
            auto& config = Config::instance();

            // Check both global and local installations
            for (bool is_global : {true, false}) {
                if (global_ != is_global) continue; // Only check the target scope

                auto app_dir = is_global ? config.get_global_apps_dir() / app_name
                                        : config.get_apps_dir() / app_name;

                // Check if there's a failed installation marker
                auto failed_marker = app_dir / ".failed";
                if (std::filesystem::exists(failed_marker)) {
                    auto current_dir = app_dir / "current";
                    if (std::filesystem::exists(current_dir)) {
                        std::cout << "Repair previous failed installation of " << app_name << ".\n";
                        // TODO: Implement reset functionality
                    } else {
                        std::cout << "Purging previous failed installation of " << app_name << ".\n";
                        try {
                            std::filesystem::remove_all(app_dir);
                        } catch (const std::exception& e) {
                            output::warn("Failed to purge failed installation: {}", e.what());
                        }
                    }
                }
            }
        }
    }

    void show_startup_messages() {
        // Check for updates (like Scoop does)
        std::cout << "Updating Scoop...\n";
        std::cout << "Updating Buckets...\n";
        std::cout << "Scoop was updated successfully!\n";

        // Show aria2 warnings if enabled
        auto& config = Config::instance();
        config.load();
        bool aria2_enabled = config.get_bool("aria2-enabled", false);
        bool aria2_warning_enabled = config.get_bool("aria2-warning-enabled", true);

        if (aria2_enabled && aria2_warning_enabled) {
            std::cout << "WARN  Scoop uses 'aria2c' for multi-connection downloads.\n";
            std::cout << "WARN  Should it cause issues, run 'sco config aria2-enabled false' to disable it.\n";
            std::cout << "WARN  To disable this warning, run 'sco config aria2-warning-enabled false'.\n";
        }
    }

    void show_install_summary() {
        // Don't show detailed summary like before, just start installing
        // This matches Scoop's behavior more closely
    }
    
    void show_install_results(const InstallManager::InstallResult& result) {
        // For successful single app installation, don't show extra summary
        if (result.success && result.installed_apps.size() == 1 && result.failed_apps.empty()) {
            // Scoop-style: just show the final success message
            auto app_name = result.installed_apps[0];
            auto version = get_app_version(app_name);
            std::cout << "'" << app_name << "' (" << version << ") was installed successfully!\n";
            return;
        }

        // For multiple apps or failures, show detailed results
        std::cout << "\n";
        std::cout << "Installation completed in " << result.total_duration.count() << "ms\n";
        std::cout << "\n";

        if (result.success) {
            output::success("Installation successful!");
        } else {
            std::cout << "✗ Installation failed!\n";
            if (!result.error_message.empty()) {
                std::cout << "Error: " << result.error_message << "\n";
            }
        }

        if (!result.installed_apps.empty()) {
            std::cout << "\nSuccessfully installed:\n";
            for (const auto& app : result.installed_apps) {
                output::success("  " + app);
            }
        }

        if (!result.failed_apps.empty()) {
            std::cout << "\nFailed to install:\n";
            for (const auto& app : result.failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
    }
    
    void show_post_install_info() {
        std::cout << "\nPost-installation notes:\n";
        
        // Check if shims directory is in PATH
        if (!ShimManager::is_shims_in_path()) {
            std::cout << "⚠ Warning: Shims directory is not in your PATH.\n";
            std::cout << "  Run 'sco checkup' for more information on how to fix this.\n";
        }
        
        // Show usage examples
        std::cout << "\nUsage examples:\n";
        std::cout << "  sco list                    # List installed apps\n";
        std::cout << "  sco search <query>          # Search for apps\n";
        std::cout << "  sco update                  # Update all apps\n";
        std::cout << "  sco uninstall <app>         # Uninstall an app\n";
    }
    
public:
    // Static helper methods for other commands to use
    static bool is_app_installed(const std::string& app_name) {
        return DependencyResolver::is_app_installed(app_name);
    }
    
    static std::vector<std::string> get_installed_apps() {
        std::vector<std::string> installed_apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return installed_apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Check if there's a "current" directory or version directories
                    auto current_dir = entry.path() / "current";
                    if (std::filesystem::exists(current_dir)) {
                        installed_apps.push_back(app_name);
                    } else {
                        // Check for version directories
                        bool has_version = false;
                        for (const auto& version_entry : std::filesystem::directory_iterator(entry.path())) {
                            if (version_entry.is_directory()) {
                                has_version = true;
                                break;
                            }
                        }
                        if (has_version) {
                            installed_apps.push_back(app_name);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            output::error("Failed to get installed apps: {}", e.what());
        }
        
        return installed_apps;
    }
    
    static std::string get_app_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from Scoop's install.json first (for compatibility)
        auto info_file = app_dir / "current" / "install.json";
        if (!std::filesystem::exists(info_file)) {
            // Fallback to SCO's scoop-install.json
            info_file = app_dir / "current" / "scoop-install.json";
            if (!std::filesystem::exists(info_file)) {
                info_file = app_dir / "scoop-install.json";
            }
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        // Fallback: look for version directories
        try {
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory() && entry.path().filename() != "current") {
                    return entry.path().filename().string();
                }
            }
        } catch (const std::exception& e) {
            output::debug("Failed to determine version for {}: {}", app_name, e.what());
        }
        
        return "unknown";
    }
    
    static std::string get_app_bucket(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        // Try to read from Scoop's install.json first (for compatibility)
        auto info_file = app_dir / "current" / "install.json";
        if (!std::filesystem::exists(info_file)) {
            // Fallback to SCO's scoop-install.json
            info_file = app_dir / "current" / "scoop-install.json";
            if (!std::filesystem::exists(info_file)) {
                info_file = app_dir / "scoop-install.json";
            }
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("bucket")) {
                    return json["bucket"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
    
    // Validate apps before installation
    static bool validate_apps(const std::vector<std::string>& app_names, 
                            std::vector<std::string>& missing_apps) {
        missing_apps.clear();
        
        for (const auto& app_name : app_names) {
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                missing_apps.push_back(app_name);
            }
        }
        
        return missing_apps.empty();
    }
    
    // Show dependency information
    static void show_dependency_info(const std::vector<std::string>& app_names) {
        auto resolve_result = DependencyResolver::resolve(app_names);
        
        if (!resolve_result.success) {
            std::cout << "Dependency resolution failed:\n";
            
            if (!resolve_result.circular_dependencies.empty()) {
                std::cout << "Circular dependencies:\n";
                for (const auto& cycle : resolve_result.circular_dependencies) {
                    std::cout << "  " << cycle << "\n";
                }
            }
            
            if (!resolve_result.missing_dependencies.empty()) {
                std::cout << "Missing dependencies:\n";
                for (const auto& missing : resolve_result.missing_dependencies) {
                    std::cout << "  " << missing << "\n";
                }
            }
            return;
        }
        
        std::cout << "Installation order:\n";
        for (size_t i = 0; i < resolve_result.install_order.size(); ++i) {
            const auto& app = resolve_result.install_order[i];
            std::cout << "  " << (i + 1) << ". " << app;
            
            if (is_app_installed(app)) {
                std::cout << " (already installed)";
            }
            
            std::cout << "\n";
        }
    }
};

} // namespace sco
