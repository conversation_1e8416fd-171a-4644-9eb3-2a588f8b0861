#pragma once

#include <memory>
#include <unordered_map>
#include <functional>
#include <sstream>
#include <algorithm>
#include <CLI/CLI.hpp>
#include "../utils/output.hpp"

#include "base_command.hpp"
#include "help_command.hpp"
#include "list_command.hpp"
#include "config_command.hpp"
#include "checkup_command.hpp"
#include "status_command.hpp"
#include "cache_command.hpp"
#include "search_command.hpp"
#include "install_command.hpp"
#include "uninstall_command.hpp"
#include "update_command.hpp"
#include "info_command.hpp"
#include "home_command.hpp"
#include "bucket_command.hpp"
#include "cat_command.hpp"
#include "prefix_command.hpp"
#include "which_command.hpp"
#include "depends_command.hpp"
#include "cleanup_command.hpp"
#include "reset_command.hpp"
#include "export_command.hpp"
#include "import_command.hpp"
#include "hold_command.hpp"
#include "download_command.hpp"
#include "create_command.hpp"
#include "alias_command.hpp"
#include "shim_command.hpp"
#include "virustotal_command.hpp"
#include "aria2_command.hpp"

namespace sco {

// Helper function to join strings
template<typename Container>
std::string join_strings(const Container& container, const std::string& delimiter) {
    if (container.empty()) return "";

    std::ostringstream oss;
    auto it = container.begin();
    oss << *it;
    ++it;

    for (; it != container.end(); ++it) {
        oss << delimiter << *it;
    }

    return oss.str();
}

class CommandManager {
public:
    CommandManager() {
        // Commands will be registered when register_commands is called
    }
    
    ~CommandManager() = default;
    
    void register_commands(CLI::App& app) {
        register_help_command(app);
        register_alias_command(app);  // Move alias before list to avoid conflicts
        register_list_command(app);
        register_install_command(app);
        register_uninstall_command(app);
        register_update_command(app);
        register_search_command(app);
        register_info_command(app);
        register_home_command(app);
        register_bucket_command(app);
        register_config_command(app);
        register_cache_command(app);
        register_cat_command(app);
        register_checkup_command(app);
        register_cleanup_command(app);
        register_create_command(app);
        register_depends_command(app);
        register_download_command(app);
        register_export_command(app);
        register_import_command(app);
        register_hold_command(app);
        register_unhold_command(app);
        register_prefix_command(app);
        register_reset_command(app);
        register_shim_command(app);
        register_status_command(app);
        register_virustotal_command(app);
        register_which_command(app);
        register_aria2_command(app);
    }
    
    int execute() {
        if (current_command_) {
            return current_command_();
        }
        // No command was selected, execute help command
        output::debug("No command selected, executing help");
        return execute_help_command();
    }

    int execute_help_command() {
        auto it = commands_.find("help");
        if (it != commands_.end()) {
            return it->second->execute();
        }
        return 0;
    }
    
private:
    std::unordered_map<std::string, std::unique_ptr<BaseCommand>> commands_;
    std::function<int()> current_command_;
    
    void register_command(const std::string& name, std::unique_ptr<BaseCommand> command) {
        commands_[name] = std::move(command);
    }
    
    void register_help_command(CLI::App& app) {
        auto help_cmd = std::make_unique<HelpCommand>();
        help_cmd->set_app(&app);
        
        auto* sub = app.add_subcommand("help", "Show help for a command");
        std::string command_name;
        sub->add_option("command", command_name, "Command to show help for");
        
        sub->callback([this, help_cmd_ptr = help_cmd.get(), command_name]() {
            help_cmd_ptr->set_command_name(command_name);
            current_command_ = [help_cmd_ptr]() { return help_cmd_ptr->execute(); };
        });
        
        register_command("help", std::move(help_cmd));
    }
    
    void register_list_command(CLI::App& app) {
        auto list_cmd = std::make_unique<ListCommand>();
        
        auto* sub = app.add_subcommand("list", "List installed apps");
        
        sub->callback([this, list_cmd_ptr = list_cmd.get()]() {
            output::debug("List command callback called");
            current_command_ = [list_cmd_ptr]() { return list_cmd_ptr->execute(); };
        });
        
        register_command("list", std::move(list_cmd));
    }
    
    void register_install_command(CLI::App& app) {
        auto install_cmd = std::make_unique<InstallCommand>();
        auto install_cmd_ptr = install_cmd.get();

        auto* sub = app.add_subcommand("install", "Install apps");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto global = std::make_shared<bool>(false);
        auto skip_deps = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto no_cache = std::make_shared<bool>(false);
        auto arch = std::make_shared<std::string>("64bit");

        sub->add_option("apps", *apps, "Apps to install")->required();
        sub->add_flag("--global,-g", *global, "Install globally");
        sub->add_flag("--skip-dependencies", *skip_deps, "Skip dependency resolution");
        sub->add_flag("--force,-f", *force, "Force reinstall");
        sub->add_flag("--no-cache", *no_cache, "Don't use cache");
        sub->add_option("--arch,-a", *arch, "Architecture (32bit, 64bit, arm64)");

        sub->callback([this, install_cmd_ptr, apps, global, skip_deps, force, no_cache, arch]() {
            install_cmd_ptr->set_apps(*apps);
            install_cmd_ptr->set_global(*global);
            install_cmd_ptr->set_skip_dependencies(*skip_deps);
            install_cmd_ptr->set_force(*force);
            install_cmd_ptr->set_no_cache(*no_cache);
            install_cmd_ptr->set_architecture(*arch);
            current_command_ = [install_cmd_ptr]() { return install_cmd_ptr->execute(); };
        });

        register_command("install", std::move(install_cmd));
    }
    
    void register_uninstall_command(CLI::App& app) {
        auto uninstall_cmd = std::make_unique<UninstallCommand>();
        auto uninstall_cmd_ptr = uninstall_cmd.get();

        auto* sub = app.add_subcommand("uninstall", "Uninstall apps");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto global = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto purge = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to uninstall")->required();
        sub->add_flag("--global,-g", *global, "Uninstall globally");
        sub->add_flag("--force,-f", *force, "Force uninstall (ignore dependents)");
        sub->add_flag("--purge,-p", *purge, "Remove all app data");

        sub->callback([this, uninstall_cmd_ptr, apps, global, force, purge]() {
            uninstall_cmd_ptr->set_apps(*apps);
            uninstall_cmd_ptr->set_global(*global);
            uninstall_cmd_ptr->set_force(*force);
            uninstall_cmd_ptr->set_purge(*purge);
            current_command_ = [uninstall_cmd_ptr]() { return uninstall_cmd_ptr->execute(); };
        });

        register_command("uninstall", std::move(uninstall_cmd));
    }
    
    void register_update_command(CLI::App& app) {
        auto update_cmd = std::make_unique<UpdateCommand>();
        auto update_cmd_ptr = update_cmd.get();

        auto* sub = app.add_subcommand("update", "Update apps, or Scoop itself");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto all = std::make_shared<bool>(false);
        auto global = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto no_cache = std::make_shared<bool>(false);
        auto skip_deps = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to update");
        sub->add_flag("--all,-a", *all, "Update all apps");
        sub->add_flag("--global,-g", *global, "Update globally installed apps");
        sub->add_flag("--force,-f", *force, "Force update even if versions match");
        sub->add_flag("--no-cache", *no_cache, "Don't use cache");
        sub->add_flag("--skip-dependencies", *skip_deps, "Skip dependency resolution");

        sub->callback([this, update_cmd_ptr, apps, all, global, force, no_cache, skip_deps]() {
            update_cmd_ptr->set_apps(*apps);
            update_cmd_ptr->set_update_all(*all);
            update_cmd_ptr->set_global(*global);
            update_cmd_ptr->set_force(*force);
            update_cmd_ptr->set_no_cache(*no_cache);
            update_cmd_ptr->set_skip_dependencies(*skip_deps);
            current_command_ = [update_cmd_ptr]() { return update_cmd_ptr->execute(); };
        });

        register_command("update", std::move(update_cmd));
    }
    
    void register_search_command(CLI::App& app) {
        auto search_cmd = std::make_unique<SearchCommand>();
        auto search_cmd_ptr = search_cmd.get();

        auto* sub = app.add_subcommand("search", "Search available apps");
        auto query = std::make_shared<std::string>();
        sub->add_option("query", *query, "Search query")->required();

        sub->callback([this, search_cmd_ptr, query]() {
            search_cmd_ptr->set_query(*query);
            current_command_ = [search_cmd_ptr]() { return search_cmd_ptr->execute(); };
        });

        register_command("search", std::move(search_cmd));
    }
    
    void register_info_command(CLI::App& app) {
        auto info_cmd = std::make_unique<InfoCommand>();
        auto info_cmd_ptr = info_cmd.get();

        auto* sub = app.add_subcommand("info", "Display information about an app");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, info_cmd_ptr, app_name]() {
            info_cmd_ptr->set_app_name(*app_name);
            current_command_ = [info_cmd_ptr]() { return info_cmd_ptr->execute(); };
        });

        register_command("info", std::move(info_cmd));
    }
    
    void register_home_command(CLI::App& app) {
        auto home_cmd = std::make_unique<HomeCommand>();
        auto home_cmd_ptr = home_cmd.get();

        auto* sub = app.add_subcommand("home", "Opens the app homepage");
        auto app_names = std::make_shared<std::vector<std::string>>();
        sub->add_option("apps", *app_names, "App names")->required();

        sub->callback([this, home_cmd_ptr, app_names]() {
            home_cmd_ptr->set_app_names(*app_names);
            current_command_ = [home_cmd_ptr]() { return home_cmd_ptr->execute(); };
        });

        register_command("home", std::move(home_cmd));
    }

    // Placeholder implementations for remaining commands
    void register_bucket_command(CLI::App& app) {
        auto bucket_cmd = std::make_unique<BucketCommand>();
        auto bucket_cmd_ptr = bucket_cmd.get();

        auto* bucket_sub = app.add_subcommand("bucket", "Manage Scoop buckets");
        bucket_sub->require_subcommand(0, 1); // Allow 0 or 1 subcommand (default to list)

        auto bucket_name = std::make_shared<std::string>();
        auto bucket_url = std::make_shared<std::string>();

        // Add list subcommand
        auto* list_sub = bucket_sub->add_subcommand("list", "List added buckets");
        list_sub->callback([this, bucket_cmd_ptr]() {
            output::debug("Bucket list command callback called");
            bucket_cmd_ptr->set_action("list");
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        // Add add subcommand
        auto* add_sub = bucket_sub->add_subcommand("add", "Add a bucket");
        add_sub->add_option("name", *bucket_name, "Bucket name")->required();
        add_sub->add_option("url", *bucket_url, "Bucket URL (optional for known buckets)");
        add_sub->callback([this, bucket_cmd_ptr, bucket_name, bucket_url]() {
            output::debug("Bucket add command callback called");
            bucket_cmd_ptr->set_action("add");
            bucket_cmd_ptr->set_bucket_name(*bucket_name);
            bucket_cmd_ptr->set_bucket_url(*bucket_url);
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        // Add remove subcommand
        auto* remove_sub = bucket_sub->add_subcommand("remove", "Remove a bucket");
        remove_sub->add_option("name", *bucket_name, "Bucket name")->required();
        remove_sub->callback([this, bucket_cmd_ptr, bucket_name]() {
            output::debug("Bucket remove command callback called");
            bucket_cmd_ptr->set_action("remove");
            bucket_cmd_ptr->set_bucket_name(*bucket_name);
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        // Add known subcommand
        auto* known_sub = bucket_sub->add_subcommand("known", "List known buckets");
        known_sub->callback([this, bucket_cmd_ptr]() {
            output::debug("Bucket known command callback called");
            bucket_cmd_ptr->set_action("known");
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        // Default callback for bucket without subcommand (defaults to list)
        bucket_sub->callback([this, bucket_cmd_ptr]() {
            output::debug("Bucket default command callback called (defaulting to list)");
            bucket_cmd_ptr->set_action("list");
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        register_command("bucket", std::move(bucket_cmd));
    }

    void register_config_command(CLI::App& app) {
        auto config_cmd = std::make_unique<ConfigCommand>();
        auto config_cmd_ptr = config_cmd.get();

        auto* sub = app.add_subcommand("config", "Get or set configuration values");
        auto key = std::make_shared<std::string>();
        auto value = std::make_shared<std::string>();
        sub->add_option("key", *key, "Configuration key");
        sub->add_option("value", *value, "Configuration value");

        sub->callback([this, config_cmd_ptr, key, value]() {
            config_cmd_ptr->set_key(*key);
            config_cmd_ptr->set_value(*value);
            current_command_ = [config_cmd_ptr]() { return config_cmd_ptr->execute(); };
        });

        register_command("config", std::move(config_cmd));
    }

    void register_cache_command(CLI::App& app) {
        auto cache_cmd = std::make_unique<CacheCommand>();

        auto* sub = app.add_subcommand("cache", "Show or clear the download cache");
        std::string action, app_name;
        sub->add_option("action", action, "Action: show, rm");
        sub->add_option("app", app_name, "App name (for rm action)");

        sub->callback([this, cache_cmd_ptr = cache_cmd.get(), action, app_name]() {
            cache_cmd_ptr->set_action(action);
            cache_cmd_ptr->set_app_name(app_name);
            current_command_ = [cache_cmd_ptr]() { return cache_cmd_ptr->execute(); };
        });

        register_command("cache", std::move(cache_cmd));
    }

    void register_cat_command(CLI::App& app) {
        auto cat_cmd = std::make_unique<CatCommand>();
        auto cat_cmd_ptr = cat_cmd.get();

        auto* sub = app.add_subcommand("cat", "Show content of specified manifest");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, cat_cmd_ptr, app_name]() {
            cat_cmd_ptr->set_app_name(*app_name);
            current_command_ = [cat_cmd_ptr]() { return cat_cmd_ptr->execute(); };
        });

        register_command("cat", std::move(cat_cmd));
    }

    void register_checkup_command(CLI::App& app) {
        auto checkup_cmd = std::make_unique<CheckupCommand>();

        auto* sub = app.add_subcommand("checkup", "Check for potential problems");

        sub->callback([this, checkup_cmd_ptr = checkup_cmd.get()]() {
            current_command_ = [checkup_cmd_ptr]() { return checkup_cmd_ptr->execute(); };
        });

        register_command("checkup", std::move(checkup_cmd));
    }

    void register_cleanup_command(CLI::App& app) {
        auto cleanup_cmd = std::make_unique<CleanupCommand>();
        auto cleanup_cmd_ptr = cleanup_cmd.get();

        auto* sub = app.add_subcommand("cleanup", "Cleanup apps by removing old versions");
        auto app_names = std::make_shared<std::vector<std::string>>();
        auto cleanup_all = std::make_shared<bool>(false);
        auto global = std::make_shared<bool>(false);
        auto keep_versions = std::make_shared<int>(1);

        sub->add_option("apps", *app_names, "Apps to cleanup");
        sub->add_flag("--all,-a", *cleanup_all, "Cleanup all apps");
        sub->add_flag("--global,-g", *global, "Cleanup globally installed apps");
        sub->add_option("--keep,-k", *keep_versions, "Number of versions to keep (default: 1)");

        sub->callback([this, cleanup_cmd_ptr, app_names, cleanup_all, global, keep_versions]() {
            cleanup_cmd_ptr->set_app_names(*app_names);
            cleanup_cmd_ptr->set_cleanup_all(*cleanup_all);
            cleanup_cmd_ptr->set_global(*global);
            cleanup_cmd_ptr->set_keep_versions(*keep_versions);
            current_command_ = [cleanup_cmd_ptr]() { return cleanup_cmd_ptr->execute(); };
        });

        register_command("cleanup", std::move(cleanup_cmd));
    }

    void register_create_command(CLI::App& app) {
        auto create_cmd = std::make_unique<CreateCommand>();
        auto create_cmd_ptr = create_cmd.get();

        auto* sub = app.add_subcommand("create", "Create a custom app manifest");
        auto app_name = std::make_shared<std::string>();
        auto output_dir = std::make_shared<std::string>();

        sub->add_option("app", *app_name, "App name")->required();
        sub->add_option("--output,-o", *output_dir, "Output directory (default: current directory)");

        sub->callback([this, create_cmd_ptr, app_name, output_dir]() {
            create_cmd_ptr->set_app_name(*app_name);
            create_cmd_ptr->set_output_dir(*output_dir);
            current_command_ = [create_cmd_ptr]() { return create_cmd_ptr->execute(); };
        });

        register_command("create", std::move(create_cmd));
    }

    void register_depends_command(CLI::App& app) {
        auto depends_cmd = std::make_unique<DependsCommand>();
        auto depends_cmd_ptr = depends_cmd.get();

        auto* sub = app.add_subcommand("depends", "List dependencies for an app");
        auto app_name = std::make_shared<std::string>();
        auto show_tree = std::make_shared<bool>(false);

        sub->add_option("app", *app_name, "App name")->required();
        sub->add_flag("--tree,-t", *show_tree, "Show dependency tree");

        sub->callback([this, depends_cmd_ptr, app_name, show_tree]() {
            depends_cmd_ptr->set_app_name(*app_name);
            depends_cmd_ptr->set_show_tree(*show_tree);
            current_command_ = [depends_cmd_ptr]() { return depends_cmd_ptr->execute(); };
        });

        register_command("depends", std::move(depends_cmd));
    }

    void register_download_command(CLI::App& app) {
        auto download_cmd = std::make_unique<DownloadCommand>();
        auto download_cmd_ptr = download_cmd.get();

        auto* sub = app.add_subcommand("download", "Download apps in the cache folder and verify hashes");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto architecture = std::make_shared<std::string>("64bit");
        auto force = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to download")->required();
        sub->add_option("--arch,-a", *architecture, "Architecture (32bit, 64bit, arm64)");
        sub->add_flag("--force,-f", *force, "Force re-download even if cached");

        sub->callback([this, download_cmd_ptr, apps, architecture, force]() {
            download_cmd_ptr->set_app_names(*apps);
            download_cmd_ptr->set_architecture(*architecture);
            download_cmd_ptr->set_force(*force);
            current_command_ = [download_cmd_ptr]() { return download_cmd_ptr->execute(); };
        });

        register_command("download", std::move(download_cmd));
    }

    void register_export_command(CLI::App& app) {
        auto export_cmd = std::make_unique<ExportCommand>();
        auto export_cmd_ptr = export_cmd.get();

        auto* sub = app.add_subcommand("export", "Exports installed apps, buckets (and optionally configs) in JSON format");
        auto output_file = std::make_shared<std::string>();
        auto include_config = std::make_shared<bool>(false);

        sub->add_option("--output,-o", *output_file, "Output file (default: stdout)");
        sub->add_flag("--config,-c", *include_config, "Include configuration");

        sub->callback([this, export_cmd_ptr, output_file, include_config]() {
            export_cmd_ptr->set_output_file(*output_file);
            export_cmd_ptr->set_include_config(*include_config);
            current_command_ = [export_cmd_ptr]() { return export_cmd_ptr->execute(); };
        });

        register_command("export", std::move(export_cmd));
    }

    void register_import_command(CLI::App& app) {
        auto import_cmd = std::make_unique<ImportCommand>();
        auto import_cmd_ptr = import_cmd.get();

        auto* sub = app.add_subcommand("import", "Imports apps, buckets and configs from a Scoopfile in JSON format");
        auto file = std::make_shared<std::string>();
        auto skip_installed = std::make_shared<bool>(false);
        auto skip_buckets = std::make_shared<bool>(false);
        auto skip_config = std::make_shared<bool>(false);

        sub->add_option("file", *file, "JSON file to import")->required();
        sub->add_flag("--skip-installed", *skip_installed, "Skip installing apps");
        sub->add_flag("--skip-buckets", *skip_buckets, "Skip adding buckets");
        sub->add_flag("--skip-config", *skip_config, "Skip importing config");

        sub->callback([this, import_cmd_ptr, file, skip_installed, skip_buckets, skip_config]() {
            import_cmd_ptr->set_import_file(*file);
            import_cmd_ptr->set_skip_installed(*skip_installed);
            import_cmd_ptr->set_skip_buckets(*skip_buckets);
            import_cmd_ptr->set_skip_config(*skip_config);
            current_command_ = [import_cmd_ptr]() { return import_cmd_ptr->execute(); };
        });

        register_command("import", std::move(import_cmd));
    }

    void register_hold_command(CLI::App& app) {
        auto hold_cmd = std::make_unique<HoldCommand>();
        auto hold_cmd_ptr = hold_cmd.get();

        auto* sub = app.add_subcommand("hold", "Hold an app to disable updates");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto global = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to hold");
        sub->add_flag("--global,-g", *global, "Hold globally installed apps");

        sub->callback([this, hold_cmd_ptr, apps, global]() {
            hold_cmd_ptr->set_app_names(*apps);
            hold_cmd_ptr->set_global(*global);
            current_command_ = [hold_cmd_ptr]() { return hold_cmd_ptr->execute(); };
        });

        register_command("hold", std::move(hold_cmd));
    }

    void register_unhold_command(CLI::App& app) {
        auto unhold_cmd = std::make_unique<UnholdCommand>();
        auto unhold_cmd_ptr = unhold_cmd.get();

        auto* sub = app.add_subcommand("unhold", "Unhold an app to enable updates");
        auto apps = std::make_shared<std::vector<std::string>>();
        sub->add_option("apps", *apps, "Apps to unhold")->required();

        sub->callback([this, unhold_cmd_ptr, apps]() {
            unhold_cmd_ptr->set_app_names(*apps);
            current_command_ = [unhold_cmd_ptr]() { return unhold_cmd_ptr->execute(); };
        });

        register_command("unhold", std::move(unhold_cmd));
    }

    void register_prefix_command(CLI::App& app) {
        auto prefix_cmd = std::make_unique<PrefixCommand>();
        auto prefix_cmd_ptr = prefix_cmd.get();

        auto* sub = app.add_subcommand("prefix", "Returns the path to the specified app");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, prefix_cmd_ptr, app_name]() {
            prefix_cmd_ptr->set_app_name(*app_name);
            current_command_ = [prefix_cmd_ptr]() { return prefix_cmd_ptr->execute(); };
        });

        register_command("prefix", std::move(prefix_cmd));
    }

    void register_reset_command(CLI::App& app) {
        auto reset_cmd = std::make_unique<ResetCommand>();
        auto reset_cmd_ptr = reset_cmd.get();

        auto* sub = app.add_subcommand("reset", "Reset an app to resolve conflicts");
        auto apps = std::make_shared<std::vector<std::string>>();
        sub->add_option("apps", *apps, "Apps to reset")->required();

        sub->callback([this, reset_cmd_ptr, apps]() {
            reset_cmd_ptr->set_app_names(*apps);
            current_command_ = [reset_cmd_ptr]() { return reset_cmd_ptr->execute(); };
        });

        register_command("reset", std::move(reset_cmd));
    }

    void register_shim_command(CLI::App& app) {
        auto shim_cmd = std::make_unique<ShimCommand>();
        auto shim_cmd_ptr = shim_cmd.get();

        auto* shim_sub = app.add_subcommand("shim", "Manipulate Scoop shims");
        shim_sub->require_subcommand(1); // Require exactly 1 subcommand

        // Add list subcommand
        auto* list_sub = shim_sub->add_subcommand("list", "List all shims");
        list_sub->callback([this, shim_cmd_ptr]() {
            output::debug("Shim list command callback called");
            shim_cmd_ptr->set_action("list");
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add add subcommand
        auto add_shim_name = std::make_shared<std::string>();
        auto add_target_path = std::make_shared<std::string>();
        auto* add_sub = shim_sub->add_subcommand("add", "Add a shim");
        add_sub->add_option("name", *add_shim_name, "Shim name")->required();
        add_sub->add_option("target", *add_target_path, "Target executable path")->required();
        add_sub->allow_extras(); // Allow extra arguments for command args
        add_sub->callback([this, shim_cmd_ptr, add_shim_name, add_target_path, add_sub]() {
            output::debug("Shim add command callback called");
            shim_cmd_ptr->set_action("add");
            shim_cmd_ptr->set_shim_name(*add_shim_name);
            shim_cmd_ptr->set_target_path(*add_target_path);

            // Get extra arguments
            auto extras = add_sub->remaining();
            shim_cmd_ptr->set_command_args(extras);

            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add remove subcommand (use separate variables to avoid conflicts)
        auto remove_shim_name = std::make_shared<std::string>();
        auto* remove_sub = shim_sub->add_subcommand("remove", "Remove a shim");
        remove_sub->add_option("name", *remove_shim_name, "Shim name")->required();
        remove_sub->callback([this, shim_cmd_ptr, remove_shim_name]() {
            output::debug("Shim remove command callback called");
            shim_cmd_ptr->set_action("remove");
            shim_cmd_ptr->set_shim_name(*remove_shim_name);
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add rm subcommand (Scoop compatibility)
        auto rm_shim_name = std::make_shared<std::string>();
        auto* rm_sub = shim_sub->add_subcommand("rm", "Remove a shim");
        rm_sub->add_option("name", *rm_shim_name, "Shim name")->required();
        rm_sub->callback([this, shim_cmd_ptr, rm_shim_name]() {
            output::debug("Shim rm command callback called");
            shim_cmd_ptr->set_action("remove");
            shim_cmd_ptr->set_shim_name(*rm_shim_name);
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add info subcommand (Scoop compatibility)
        auto info_shim_name = std::make_shared<std::string>();
        auto* info_sub = shim_sub->add_subcommand("info", "Show shim information");
        info_sub->add_option("name", *info_shim_name, "Shim name")->required();
        info_sub->callback([this, shim_cmd_ptr, info_shim_name]() {
            output::debug("Shim info command callback called");
            shim_cmd_ptr->set_action("info");
            shim_cmd_ptr->set_shim_name(*info_shim_name);
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add alter subcommand (Scoop compatibility)
        auto alter_shim_name = std::make_shared<std::string>();
        auto* alter_sub = shim_sub->add_subcommand("alter", "Alternate shim target source");
        alter_sub->add_option("name", *alter_shim_name, "Shim name")->required();
        alter_sub->callback([this, shim_cmd_ptr, alter_shim_name]() {
            output::debug("Shim alter command callback called");
            shim_cmd_ptr->set_action("alter");
            shim_cmd_ptr->set_shim_name(*alter_shim_name);
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // Add repair subcommand
        auto* repair_sub = shim_sub->add_subcommand("repair", "Repair all shims");
        repair_sub->callback([this, shim_cmd_ptr]() {
            output::debug("Shim repair command callback called");
            shim_cmd_ptr->set_action("repair");
            current_command_ = [shim_cmd_ptr]() { return shim_cmd_ptr->execute(); };
        });

        // No default callback - require explicit subcommand

        register_command("shim", std::move(shim_cmd));
    }

    void register_status_command(CLI::App& app) {
        auto status_cmd = std::make_unique<StatusCommand>();

        auto* sub = app.add_subcommand("status", "Show status and check for new app versions");

        sub->callback([this, status_cmd_ptr = status_cmd.get()]() {
            current_command_ = [status_cmd_ptr]() { return status_cmd_ptr->execute(); };
        });

        register_command("status", std::move(status_cmd));
    }

    void register_virustotal_command(CLI::App& app) {
        auto vt_cmd = std::make_unique<VirusTotalCommand>();
        auto vt_cmd_ptr = vt_cmd.get();

        auto* sub = app.add_subcommand("virustotal", "Look for app's hash or url on virustotal.com");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto architecture = std::make_shared<std::string>("64bit");
        auto no_browser = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to check")->required();
        sub->add_option("--arch,-a", *architecture, "Architecture (32bit, 64bit, arm64)");
        sub->add_flag("--no-browser", *no_browser, "Don't open browser automatically");

        sub->callback([this, vt_cmd_ptr, apps, architecture, no_browser]() {
            vt_cmd_ptr->set_app_names(*apps);
            vt_cmd_ptr->set_architecture(*architecture);
            vt_cmd_ptr->set_no_browser(*no_browser);
            current_command_ = [vt_cmd_ptr]() { return vt_cmd_ptr->execute(); };
        });

        register_command("virustotal", std::move(vt_cmd));
    }

    void register_which_command(CLI::App& app) {
        auto which_cmd = std::make_unique<WhichCommand>();
        auto which_cmd_ptr = which_cmd.get();

        auto* sub = app.add_subcommand("which", "Locate a shim/executable (similar to 'which' on Linux)");
        auto command = std::make_shared<std::string>();
        sub->add_option("command", *command, "Command to locate")->required();

        sub->callback([this, which_cmd_ptr, command]() {
            which_cmd_ptr->set_command_name(*command);
            current_command_ = [which_cmd_ptr]() { return which_cmd_ptr->execute(); };
        });

        register_command("which", std::move(which_cmd));
    }

    void register_alias_command(CLI::App& app) {
        auto alias_cmd = std::make_unique<AliasCommand>();
        auto alias_cmd_ptr = alias_cmd.get();

        auto* sub = app.add_subcommand("alias", "Manage scoop aliases");
        auto args = std::make_shared<std::vector<std::string>>();

        // Allow extras and add remaining arguments as a vector
        sub->allow_extras();
        sub->add_option("args", *args, "Arguments")->expected(0, 3);

        sub->callback([this, alias_cmd_ptr, args, sub]() {
            // Get extras (additional arguments not captured by options)
            auto extras = sub->remaining();

            // Combine args and extras
            std::vector<std::string> all_args = *args;
            all_args.insert(all_args.end(), extras.begin(), extras.end());

            if (all_args.empty()) {
                alias_cmd_ptr->set_action("");
            } else {
                alias_cmd_ptr->set_action(all_args[0]);
                if (all_args.size() > 1) {
                    alias_cmd_ptr->set_alias_name(all_args[1]);
                }
                if (all_args.size() > 2) {
                    alias_cmd_ptr->set_command(all_args[2]);
                }
            }
            current_command_ = [alias_cmd_ptr]() { return alias_cmd_ptr->execute(); };
        });

        register_command("alias", std::move(alias_cmd));
    }

    void register_aria2_command(CLI::App& app) {
        auto aria2_cmd = std::make_unique<Aria2Command>();

        auto* sub = app.add_subcommand("aria2", "Show aria2 status and configuration");

        sub->callback([this, aria2_cmd_ptr = aria2_cmd.get()]() {
            current_command_ = [aria2_cmd_ptr]() { return aria2_cmd_ptr->execute(); };
        });

        register_command("aria2", std::move(aria2_cmd));
    }
};

} // namespace sco
