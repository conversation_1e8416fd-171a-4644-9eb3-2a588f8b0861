#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <string>
#include <map>
#include <cstdlib>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class BucketCommand : public BaseCommand {
public:
    BucketCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "list") {
                return list_buckets();
            } else if (action_ == "add") {
                return add_bucket();
            } else if (action_ == "remove" || action_ == "rm") {
                return remove_bucket();
            } else if (action_ == "known") {
                return list_known_buckets();
            } else {
                output::error("scoop bucket: cmd '{}' not supported", action_);
                show_usage();
                return 1;
            }
        } catch (const std::exception& e) {
            output::error("Bucket command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "bucket"; }
    std::string get_description() const override { return "Manage Scoop buckets"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_bucket_name(const std::string& name) { bucket_name_ = name; }
    void set_bucket_url(const std::string& url) { bucket_url_ = url; }
    
private:
    std::string action_;
    std::string bucket_name_;
    std::string bucket_url_;
    
    void show_usage() {
        std::cout << "Usage:\n";
        std::cout << "  sco bucket [list]              # List added buckets\n";
        std::cout << "  sco bucket add <name> [<url>]  # Add a bucket\n";
        std::cout << "  sco bucket remove <name>       # Remove a bucket\n";
        std::cout << "  sco bucket known               # List known buckets\n";
    }
    
    int list_buckets() {
        std::cout << "Added buckets:\n\n";
        
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            std::cout << "No buckets directory found.\n";
            std::cout << "Run 'sco bucket add main' to add the main bucket.\n";
            return 0;
        }
        
        std::vector<BucketInfo> buckets;
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (entry.is_directory()) {
                    std::string bucket_name = entry.path().filename().string();
                    BucketInfo info = get_bucket_info(entry.path(), bucket_name);
                    buckets.push_back(info);
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error reading buckets directory: {}", e.what());
            return 1;
        }
        
        if (buckets.empty()) {
            std::cout << "No buckets are currently added.\n";
            std::cout << "Run 'sco bucket add main' to add the main bucket.\n";
            return 0;
        }
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Source", true);
        table.add_column("Updated", true);
        table.add_column("Manifests", true);
        
        for (const auto& bucket : buckets) {
            table.add_row({
                bucket.name,
                bucket.source,
                bucket.last_updated,
                std::to_string(bucket.manifest_count)
            });
        }
        
        table.print();
        
        std::cout << "\nTotal: " << buckets.size() << " bucket(s)\n";
        return 0;
    }
    
    int add_bucket() {
        if (bucket_name_.empty()) {
            std::cout << "Bucket name is required.\n";
            std::cout << "Usage: sco bucket add <name> [<url>]\n";
            return 1;
        }
        
        std::cout << "Adding bucket '" << bucket_name_ << "'...\n";
        
        // Get bucket URL
        std::string url = bucket_url_;
        if (url.empty()) {
            url = get_known_bucket_url(bucket_name_);
            if (url.empty()) {
                std::cout << "Unknown bucket '" << bucket_name_ << "'. Please provide a URL.\n";
                std::cout << "Usage: sco bucket add " << bucket_name_ << " <url>\n";
                return 1;
            }
        }
        
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        auto bucket_dir = buckets_dir / bucket_name_;
        
        // Check if bucket already exists
        if (std::filesystem::exists(bucket_dir)) {
            std::cout << "Bucket '" << bucket_name_ << "' is already added.\n";
            return 1;
        }
        
        // Create buckets directory if it doesn't exist
        std::filesystem::create_directories(buckets_dir);
        
        // Clone the bucket repository
        std::string command = "git clone --depth=1 \"" + url + "\" \"" + bucket_dir.string() + "\"";
        output::debug("Executing: {}", command);
        
        int result = std::system(command.c_str());
        
        if (result == 0) {
            std::cout << "Bucket '" << bucket_name_ << "' added successfully.\n";
            
            // Show bucket info
            auto info = get_bucket_info(bucket_dir, bucket_name_);
            std::cout << "Source: " << info.source << "\n";
            std::cout << "Manifests: " << info.manifest_count << "\n";
            
            return 0;
        } else {
            std::cout << "Failed to add bucket '" << bucket_name_ << "'.\n";
            std::cout << "Make sure git is installed and the URL is correct.\n";
            
            // Clean up failed clone
            if (std::filesystem::exists(bucket_dir)) {
                try {
                    std::filesystem::remove_all(bucket_dir);
                } catch (const std::exception& e) {
                    output::warn("Failed to clean up failed bucket clone: {}", e.what());
                }
            }
            
            return 1;
        }
    }
    
    int remove_bucket() {
        if (bucket_name_.empty()) {
            std::cout << "Bucket name is required.\n";
            std::cout << "Usage: sco bucket remove <name>\n";
            return 1;
        }
        
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        auto bucket_dir = buckets_dir / bucket_name_;
        
        if (!std::filesystem::exists(bucket_dir)) {
            std::cout << "Bucket '" << bucket_name_ << "' is not added.\n";
            return 1;
        }
        
        std::cout << "Removing bucket '" << bucket_name_ << "'...\n";
        
        try {
            std::filesystem::remove_all(bucket_dir);
            std::cout << "Bucket '" << bucket_name_ << "' removed successfully.\n";
            return 0;
        } catch (const std::exception& e) {
            std::cout << "Failed to remove bucket '" << bucket_name_ << "': " << e.what() << "\n";
            return 1;
        }
    }
    
    int list_known_buckets() {
        std::cout << "Known buckets:\n\n";
        
        auto known_buckets = get_known_buckets();
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Description", true);
        table.add_column("URL", true);
        
        for (const auto& bucket : known_buckets) {
            table.add_row({
                bucket.first,
                bucket.second.description,
                bucket.second.url
            });
        }
        
        table.print();
        
        std::cout << "\nTo add a bucket, run: sco bucket add <name>\n";
        return 0;
    }
    
    struct BucketInfo {
        std::string name;
        std::string source;
        std::string last_updated;
        int manifest_count = 0;
    };
    
    struct KnownBucket {
        std::string description;
        std::string url;
    };
    
    BucketInfo get_bucket_info(const std::filesystem::path& bucket_path, const std::string& name) {
        BucketInfo info;
        info.name = name;
        
        // Get source URL from git config
        auto git_config = bucket_path / ".git" / "config";
        if (std::filesystem::exists(git_config)) {
            try {
                std::ifstream file(git_config);
                std::string line;
                while (std::getline(file, line)) {
                    if (line.find("url = ") != std::string::npos) {
                        info.source = line.substr(line.find("url = ") + 6);
                        break;
                    }
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read git config for bucket {}: {}", name, e.what());
                info.source = "Unknown";
            }
        } else {
            info.source = "Local";
        }
        
        // Get last updated time
        try {
            auto last_write = std::filesystem::last_write_time(bucket_path);
            // Simplified time formatting
            info.last_updated = "Recently";
        } catch (const std::exception& e) {
            info.last_updated = "Unknown";
        }
        
        // Count manifests
        auto bucket_dir = bucket_path / "bucket";
        if (std::filesystem::exists(bucket_dir)) {
            try {
                for (const auto& entry : std::filesystem::directory_iterator(bucket_dir)) {
                    if (entry.is_regular_file() && entry.path().extension() == ".json") {
                        info.manifest_count++;
                    }
                }
            } catch (const std::exception& e) {
                output::debug("Failed to count manifests for bucket {}: {}", name, e.what());
            }
        }
        
        return info;
    }
    
    std::string get_known_bucket_url(const std::string& bucket_name) {
        auto known_buckets = get_known_buckets();
        auto it = known_buckets.find(bucket_name);
        if (it != known_buckets.end()) {
            return it->second.url;
        }
        return "";
    }
    
    std::map<std::string, KnownBucket> get_known_buckets() {
        std::map<std::string, KnownBucket> buckets;
        
        // Well-known Scoop buckets
        buckets["main"] = {"The default bucket for the most common (mostly CLI) apps", 
                          "https://github.com/ScoopInstaller/Main"};
        buckets["extras"] = {"Apps that don't fit the main bucket's criteria", 
                            "https://github.com/ScoopInstaller/Extras"};
        buckets["versions"] = {"Alternative versions of apps found in other buckets", 
                              "https://github.com/ScoopInstaller/Versions"};
        buckets["nirsoft"] = {"Nirsoft utilities", 
                             "https://github.com/kodybrown/scoop-nirsoft"};
        buckets["php"] = {"Installers for most versions of PHP", 
                         "https://github.com/ScoopInstaller/PHP"};
        buckets["nerd-fonts"] = {"Nerd Fonts", 
                                "https://github.com/matthewjberger/scoop-nerd-fonts"};
        buckets["nonportable"] = {"Non-portable apps (may require UAC)", 
                                 "https://github.com/ScoopInstaller/Nonportable"};
        buckets["java"] = {"Installers for Oracle Java, OpenJDK, Eclipse Temurin, IBM Semeru, etc.", 
                          "https://github.com/ScoopInstaller/Java"};
        buckets["games"] = {"Open source/freeware games and game-related tools", 
                           "https://github.com/Calinou/scoop-games"};
        
        return buckets;
    }
};

} // namespace sco
