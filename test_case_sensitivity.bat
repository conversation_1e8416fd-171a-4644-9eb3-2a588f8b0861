@echo off
echo === Testing Case Sensitivity and Command Variations ===
echo.

echo Step 1: Clean install
.\Build\Release\sco.exe uninstall notepad2 >nul 2>&1
.\Build\Release\sco.exe install notepad2 >nul 2>&1
echo Installation completed.
echo.

echo Step 2: Test different command variations
echo Testing notepad2 lowercase:
notepad2 --help >nul 2>&1
if %errorlevel%==0 (
    echo SUCCESS - notepad2 lowercase works
) else (
    echo FAILED - notepad2 lowercase not found
)

echo Testing Notepad2 capitalized:
Notepad2 --help >nul 2>&1
if %errorlevel%==0 (
    echo SUCCESS - Notepad2 capitalized works
) else (
    echo FAILED - Notepad2 capitalized not found
)

echo Testing NOTEPAD2 uppercase:
NOTEPAD2 --help >nul 2>&1
if %errorlevel%==0 (
    echo SUCCESS - NOTEPAD2 uppercase works
) else (
    echo FAILED - NOTEPAD2 uppercase not found
)
echo.

echo Step 3: Check what files exist in shims directory
echo Files in shims directory:
dir "C:\Users\<USER>\scoop\shims\*notepad*" /b 2>nul
echo.

echo Step 4: Test with full path
echo Testing full path:
"C:\Users\<USER>\scoop\shims\Notepad2.exe" --help >nul 2>&1
if %errorlevel%==0 (
    echo SUCCESS - Full path works
) else (
    echo FAILED - Full path doesn't work
)
echo.

echo Step 5: Check PATH order
echo PATH entries containing scoop:
echo %PATH% | findstr /i "scoop"
echo.

echo Test completed.
pause
