Write-Host "=== Testing Environment Variable Refresh ===" -ForegroundColor Green

# Step 1: Clean up - remove SCO shims from registry PATH
Write-Host "`nStep 1: Cleaning up registry PATH..." -ForegroundColor Yellow
try {
    $currentPath = (Get-ItemProperty -Path "HKCU:\Environment" -Name "PATH" -ErrorAction SilentlyContinue).PATH
    if ($currentPath) {
        $pathEntries = $currentPath.Split(';')
        $cleanedEntries = $pathEntries | Where-Object { $_ -notlike "*scoop\shims*" }
        $newPath = $cleanedEntries -join ';'
        
        Set-ItemProperty -Path "HKCU:\Environment" -Name "PATH" -Value $newPath
        Write-Host "Cleaned registry PATH" -ForegroundColor Green
    }
} catch {
    Write-Host "Error cleaning PATH: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 2: Check current process PATH before
Write-Host "`nStep 2: Checking current process PATH before..." -ForegroundColor Yellow
$pathBefore = $env:PATH
$scoShimsInProcessBefore = $pathBefore -like "*scoop\shims*"
Write-Host "SCO shims in current process PATH before: $(if ($scoShimsInProcessBefore) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($scoShimsInProcessBefore) { 'Green' } else { 'Red' })

# Step 3: Uninstall notepad2 to ensure clean state
Write-Host "`nStep 3: Ensuring clean state..." -ForegroundColor Yellow
try {
    $output = & ".\Build\Release\sco.exe" uninstall notepad2 2>&1
    Write-Host "Uninstall result: $($output -join ' ')" -ForegroundColor Cyan
} catch {
    Write-Host "Uninstall not needed or failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 4: Install notepad2 (this should add shims to PATH and refresh current process)
Write-Host "`nStep 4: Installing notepad2 with environment refresh..." -ForegroundColor Yellow
try {
    $output = & ".\Build\Release\sco.exe" --verbose install notepad2 2>&1
    Write-Host "Install output:" -ForegroundColor Cyan
    $output | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} catch {
    Write-Host "Error during install: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Check current process PATH after
Write-Host "`nStep 5: Checking current process PATH after..." -ForegroundColor Yellow
$pathAfter = $env:PATH
$scoShimsInProcessAfter = $pathAfter -like "*scoop\shims*"
Write-Host "SCO shims in current process PATH after: $(if ($scoShimsInProcessAfter) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($scoShimsInProcessAfter) { 'Green' } else { 'Red' })

# Step 6: Test if notepad2 command works in current session
Write-Host "`nStep 6: Testing notepad2 command in current session..." -ForegroundColor Yellow
try {
    $notepadPath = Get-Command notepad2 -ErrorAction SilentlyContinue
    if ($notepadPath) {
        Write-Host "SUCCESS: notepad2 command found at: $($notepadPath.Source)" -ForegroundColor Green
    } else {
        Write-Host "FAILED: notepad2 command not found in current session" -ForegroundColor Red
    }
} catch {
    Write-Host "FAILED: Error testing notepad2 command: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Summary
Write-Host "`n=== Summary ===" -ForegroundColor Magenta
Write-Host "Environment refresh test completed." -ForegroundColor White
if ($scoShimsInProcessAfter -and -not $scoShimsInProcessBefore) {
    Write-Host "✓ SUCCESS: Current process PATH was refreshed!" -ForegroundColor Green
} elseif ($scoShimsInProcessAfter -and $scoShimsInProcessBefore) {
    Write-Host "? INFO: SCO shims were already in current process PATH" -ForegroundColor Yellow
} else {
    Write-Host "✗ FAILED: Current process PATH was not refreshed" -ForegroundColor Red
}
