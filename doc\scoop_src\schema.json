{"$id": "http://scoop.sh/draft/schema#", "$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "definitions": {"hashPattern": {"pattern": "^([a-fA-F0-9]{64}|(sha1|sha256|sha512|md5):([a-fA-F0-9]{32}|[a-fA-F0-9]{40}|[a-fA-F0-9]{64}|[a-fA-F0-9]{128}))$", "type": "string"}, "jsonPathPattern": {"pattern": "^\\$[.\\[].*$", "type": "string"}, "hash": {"anyOf": [{"$ref": "#/definitions/hashPattern"}, {"items": {"$ref": "#/definitions/hashPattern"}, "minItems": 1, "type": "array", "uniqueItems": true}]}, "hashExtraction": {"additionalProperties": false, "properties": {"find": {"format": "regex", "type": "string", "description": "Same as 'regex'"}, "regex": {"format": "regex", "type": "string"}, "jp": {"$ref": "#/definitions/jsonPathPattern", "description": "Same as 'j<PERSON><PERSON>'"}, "jsonpath": {"$ref": "#/definitions/jsonPathPattern"}, "xpath": {"type": "string"}, "mode": {"enum": ["download", "extract", "json", "xpath", "rdf", "metalink", "fosshub", "sourceforge"]}, "type": {"enum": ["md5", "sha1", "sha256", "sha512"], "description": "Deprecated, hash type is determined automatically"}, "url": {"anyOf": [{"format": "uri", "type": "string"}, {"pattern": "^(\\$url|\\$baseurl).[\\w\\d]+$", "type": "string"}, {"pattern": "^.*(\\$url|\\$baseurl).*$", "type": "string"}]}}, "type": "object"}, "hashExtractionOrArrayOfHashExtractions": {"anyOf": [{"$ref": "#/definitions/hashExtraction"}, {"items": {"$ref": "#/definitions/hashExtraction"}, "minItems": 1, "type": "array", "uniqueItems": false}]}, "architecture": {"additionalProperties": false, "properties": {"bin": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "checkver": {"$ref": "#/definitions/checkver"}, "env_add_path": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "env_set": {"type": "object"}, "extract_dir": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "hash": {"$ref": "#/definitions/hash"}, "installer": {"$ref": "#/definitions/installer"}, "post_install": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "post_uninstall": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "pre_install": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "pre_uninstall": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "shortcuts": {"$ref": "#/definitions/shortcutsArray"}, "uninstaller": {"$ref": "#/definitions/uninstaller"}, "url": {"$ref": "#/definitions/uriOrArrayOfUris"}}, "type": "object"}, "arrayOfArrayOfStrings": {"items": {"items": {"type": "string"}, "minItems": 1, "type": "array"}, "minItems": 1, "type": "array"}, "shortcutsArray": {"items": {"items": {"type": "string"}, "minItems": 2, "maxItems": 4, "type": "array"}, "minItems": 1, "type": "array"}, "autoupdateArch": {"type": "object", "additionalProperties": false, "properties": {"bin": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "env_add_path": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "env_set": {"type": "object"}, "extract_dir": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "hash": {"$ref": "#/definitions/hashExtractionOrArrayOfHashExtractions"}, "installer": {"type": "object", "additionalProperties": false, "properties": {"file": {"type": "string"}}}, "shortcuts": {"$ref": "#/definitions/shortcutsArray"}, "url": {"$ref": "#/definitions/autoupdateUriOrArrayOfAutoupdateUris"}}}, "autoupdate": {"type": "object", "additionalProperties": false, "properties": {"architecture": {"type": "object", "additionalProperties": false, "properties": {"32bit": {"$ref": "#/definitions/autoupdateArch"}, "64bit": {"$ref": "#/definitions/autoupdateArch"}, "arm64": {"$ref": "#/definitions/autoupdateArch"}}}, "bin": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "env_add_path": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "env_set": {"type": "object"}, "extract_dir": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "hash": {"$ref": "#/definitions/hashExtractionOrArrayOfHashExtractions"}, "installer": {"type": "object", "additionalProperties": false, "properties": {"file": {"type": "string"}}}, "license": {"$ref": "#/definitions/license"}, "notes": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "persist": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "psmodule": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}}}, "shortcuts": {"$ref": "#/definitions/shortcutsArray"}, "url": {"$ref": "#/definitions/autoupdateUriOrArrayOfAutoupdateUris"}}}, "checkver": {"anyOf": [{"format": "regex", "type": "string"}, {"additionalProperties": false, "properties": {"github": {"format": "uri", "type": "string"}, "re": {"format": "regex", "type": "string", "description": "Same as 'regex'"}, "regex": {"format": "regex", "type": "string"}, "url": {"format": "uri", "type": "string"}, "jp": {"$ref": "#/definitions/jsonPathPattern", "description": "Same as 'j<PERSON><PERSON>'"}, "jsonpath": {"$ref": "#/definitions/jsonPathPattern"}, "xpath": {"type": "string"}, "reverse": {"description": "Reverse the order of regex matches", "type": "boolean"}, "replace": {"description": "Allows rearrange the regexp matches", "type": "string"}, "useragent": {"type": "string"}, "script": {"$ref": "#/definitions/stringOrArrayOfStrings", "description": "Custom PowerShell script to retrieve application version using more complex approach."}, "sourceforge": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "properties": {"project": {"type": "string"}, "path": {"type": "string"}}, "type": "object"}]}}, "type": "object"}]}, "installer": {"additionalProperties": false, "properties": {"_comment": {"description": "Undocumented: only used in scoop-extras/oraclejdk* and scoop-extras/appengine-go", "type": "string"}, "args": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "file": {"type": "string"}, "script": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "keep": {"type": "boolean"}}, "type": "object"}, "stringOrArrayOfStrings": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "minItems": 1, "type": "array"}]}, "stringOrArrayOfStringsOrAnArrayOfArrayOfStrings": {"anyOf": [{"type": "string"}, {"items": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "minItems": 1, "type": "array"}]}, "uninstaller": {"properties": {"args": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "file": {"type": "string"}, "script": {"$ref": "#/definitions/stringOrArrayOfStrings"}}, "oneOf": [{"required": ["file"]}, {"required": ["script"]}], "type": "object"}, "uriOrArrayOfUris": {"anyOf": [{"format": "uri", "not": {"pattern": "(\\$)"}, "type": "string"}, {"items": {"format": "uri", "not": {"pattern": "(\\$)"}, "type": "string"}, "minItems": 1, "type": "array", "uniqueItems": true}]}, "autoupdateUriOrArrayOfAutoupdateUris": {"anyOf": [{"format": "uri", "type": "string"}, {"items": {"format": "uri", "type": "string"}, "minItems": 1, "type": "array", "uniqueItems": true}]}, "licenseIdentifiers": {"type": "string", "description": "License identifier based on SPDX License List https://spdx.org/licenses/", "examples": ["Apache-2.0", "BSD-3-<PERSON><PERSON>", "Freeware", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "ISC", "LGPL-2.0-only", "LGPL-2.0-or-later", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MIT", "MS-PL", "Proprietary", "Public Domain", "Shareware", "Unlicense"]}, "license": {"anyOf": [{"$ref": "#/definitions/licenseIdentifiers"}, {"additionalProperties": false, "properties": {"url": {"format": "uri", "type": "string"}, "identifier": {"$ref": "#/definitions/licenseIdentifiers"}}, "required": ["identifier"], "type": "object"}]}}, "properties": {"$schema": {"type": "string", "format": "uri"}, "_comment": {"description": "Deprecated. Use ## instead.", "$ref": "#/definitions/stringOrArrayOfStrings"}, "##": {"description": "A comment.", "$ref": "#/definitions/stringOrArrayOfStrings"}, "architecture": {"additionalProperties": false, "properties": {"32bit": {"$ref": "#/definitions/architecture"}, "64bit": {"$ref": "#/definitions/architecture"}, "arm64": {"$ref": "#/definitions/architecture"}}, "type": "object"}, "autoupdate": {"$ref": "#/definitions/autoupdate"}, "bin": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "persist": {"$ref": "#/definitions/stringOrArrayOfStringsOrAnArrayOfArrayOfStrings"}, "checkver": {"$ref": "#/definitions/checkver"}, "cookie": {"description": "Undocumented: Found at https://github.com/se35710/scoop-java/search?l=JSON&q=cookie", "type": "object"}, "depends": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "description": {"type": "string"}, "env_add_path": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "env_set": {"type": "object"}, "extract_dir": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "extract_to": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "hash": {"$ref": "#/definitions/hash"}, "homepage": {"format": "uri", "type": "string"}, "innosetup": {"description": "True if the installer InnoSetup based. Found in https://github.com/ScoopInstaller/Main/search?l=JSON&q=innosetup", "type": "boolean"}, "installer": {"$ref": "#/definitions/installer"}, "license": {"$ref": "#/definitions/license"}, "notes": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "post_install": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "post_uninstall": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "pre_install": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "pre_uninstall": {"$ref": "#/definitions/stringOrArrayOfStrings"}, "psmodule": {"additionalProperties": false, "properties": {"name": {"type": "string"}}, "type": "object"}, "shortcuts": {"$ref": "#/definitions/shortcutsArray"}, "suggest": {"additionalProperties": false, "patternProperties": {"^(.*)$": {"$ref": "#/definitions/stringOrArrayOfStrings"}}, "type": "object"}, "uninstaller": {"$ref": "#/definitions/uninstaller"}, "url": {"$ref": "#/definitions/uriOrArrayOfUris"}, "version": {"pattern": "^[\\w\\.\\-+_]+$", "type": "string"}}, "if": {"properties": {"architecture": {"properties": {"64bit": {"properties": {"url": false}}, "32bit": {"properties": {"url": false}}, "arm64": {"properties": {"url": false}}}}}}, "then": {"required": ["url"]}, "required": ["version", "homepage", "license"], "title": "scoop app manifest schema", "type": "object"}