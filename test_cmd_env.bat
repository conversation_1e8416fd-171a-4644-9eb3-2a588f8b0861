@echo off
echo === Testing CMD Environment Variable Refresh ===
echo.

echo Step 1: Check current PATH before install
echo Current PATH contains scoop\shims:
echo %PATH% | findstr /i "scoop\\shims" >nul
if %errorlevel%==0 (
    echo YES - scoop\shims found in PATH
) else (
    echo NO - scoop\shims not found in PATH
)
echo.

echo Step 2: Clean up previous installation
echo Uninstalling notepad2...
.\Build\Release\sco.exe uninstall notepad2
echo.

echo Step 3: Install notepad2 with SCO
echo Installing notepad2...
.\Build\Release\sco.exe --verbose install notepad2
echo.

echo Step 4: Check PATH after install
echo Current PATH contains scoop\shims:
echo %PATH% | findstr /i "scoop\\shims" >nul
if %errorlevel%==0 (
    echo YES - scoop\shims found in PATH after install
) else (
    echo NO - scoop\shims not found in PATH after install
)
echo.

echo Step 5: Test notepad2 command
echo Testing notepad2 command...
where notepad2 >nul 2>&1
if %errorlevel%==0 (
    echo SUCCESS - notepad2 command found
    where notepad2
) else (
    echo FAILED - notepad2 command not found
)
echo.

echo Step 6: Manual PATH check
echo Full PATH variable:
echo %PATH%
echo.

echo Step 7: Check if shim file exists
if exist "C:\Users\<USER>\scoop\shims\Notepad2.exe" (
    echo SUCCESS - Shim file exists at C:\Users\<USER>\scoop\shims\Notepad2.exe
) else (
    echo FAILED - Shim file does not exist
)
echo.

echo Test completed.
pause
