Write-Host "=== Testing SCO PATH Fix ===" -ForegroundColor Green

# Check current registry PATH
Write-Host "`nChecking current user registry PATH..." -ForegroundColor Yellow
try {
    $registryPath = (Get-ItemProperty -Path "HKCU:\Environment" -Name "PATH" -ErrorAction SilentlyContinue).PATH
    if ($registryPath) {
        $scoShimsInRegistry = $registryPath -like "*scoop\shims*"
        Write-Host "SCO shims in registry PATH: $(if ($scoShimsInRegistry) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($scoShimsInRegistry) { 'Green' } else { 'Red' })
        
        if ($scoShimsInRegistry) {
            Write-Host "Registry PATH entries containing 'scoop\shims':" -ForegroundColor Cyan
            $registryPath.Split(';') | Where-Object { $_ -like "*scoop\shims*" } | ForEach-Object {
                Write-Host "  $_" -ForegroundColor White
            }
        }
    } else {
        Write-Host "No user PATH found in registry" -ForegroundColor Red
    }
} catch {
    Write-Host "Error reading registry PATH: $($_.Exception.Message)" -ForegroundColor Red
}

# Test SCO install
Write-Host "`nTesting SCO install..." -ForegroundColor Yellow
try {
    $output = & ".\Build\Release\sco.exe" --verbose install notepad2 2>&1
    Write-Host "SCO install output:" -ForegroundColor Cyan
    $output | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} catch {
    Write-Host "Error running SCO: $($_.Exception.Message)" -ForegroundColor Red
}

# Check registry PATH again
Write-Host "`nChecking registry PATH after SCO install..." -ForegroundColor Yellow
try {
    $registryPathAfter = (Get-ItemProperty -Path "HKCU:\Environment" -Name "PATH" -ErrorAction SilentlyContinue).PATH
    if ($registryPathAfter) {
        $scoShimsInRegistryAfter = $registryPathAfter -like "*scoop\shims*"
        Write-Host "SCO shims in registry PATH after: $(if ($scoShimsInRegistryAfter) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($scoShimsInRegistryAfter) { 'Green' } else { 'Red' })
        
        if ($scoShimsInRegistryAfter) {
            Write-Host "Registry PATH entries containing 'scoop\shims' after:" -ForegroundColor Cyan
            $registryPathAfter.Split(';') | Where-Object { $_ -like "*scoop\shims*" } | ForEach-Object {
                Write-Host "  $_" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "Error reading registry PATH after: $($_.Exception.Message)" -ForegroundColor Red
}
