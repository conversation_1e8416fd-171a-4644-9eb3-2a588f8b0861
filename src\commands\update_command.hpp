#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/update_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class UpdateCommand : public BaseCommand {
public:
    UpdateCommand() = default;
    
    int execute() override {
        try {
            output::debug("Update command called");
            
            if (update_all_) {
                return update_all_apps();
            } else if (!apps_.empty()) {
                return update_specific_apps();
            } else {
                return update_scoop_itself();
            }
            
        } catch (const std::exception& e) {
            output::error("Update command failed: {}", e.what());
            std::cerr << "Update failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "update"; }
    std::string get_description() const override { return "Update apps, or Scoop itself"; }
    
    // Setters for command options
    void set_apps(const std::vector<std::string>& apps) { apps_ = apps; }
    void set_update_all(bool update_all) { update_all_ = update_all; }
    void set_global(bool global) { global_ = global; }
    void set_force(bool force) { force_ = force; }
    void set_no_cache(bool no_cache) { no_cache_ = no_cache; }
    void set_skip_dependencies(bool skip) { skip_dependencies_ = skip; }
    
private:
    std::vector<std::string> apps_;
    bool update_all_ = false;
    bool global_ = false;
    bool force_ = false;
    bool no_cache_ = false;
    bool skip_dependencies_ = false;
    
    int update_all_apps() {
        std::cout << "Updating all installed apps...\n";
        
        // Get list of installed apps
        auto installed_apps = get_installed_apps();
        if (installed_apps.empty()) {
            std::cout << "No apps are currently installed.\n";
            return 0;
        }
        
        std::cout << "Found " << installed_apps.size() << " installed app(s).\n\n";
        
        // Check for updates
        auto updates_available = check_for_updates(installed_apps);
        if (updates_available.empty()) {
            std::cout << "All apps are up to date.\n";
            return 0;
        }
        
        // Show what will be updated
        show_update_summary(updates_available);
        
        // Perform updates
        UpdateManager::UpdateOptions options;
        options.global = global_;
        options.force = force_;
        options.no_cache = no_cache_;
        options.skip_dependencies = skip_dependencies_;
        
        auto start_time = std::chrono::steady_clock::now();
        auto result = UpdateManager::update_apps(updates_available, options);
        auto end_time = std::chrono::steady_clock::now();
        
        show_update_results(result, start_time, end_time);
        
        return result.success ? 0 : 1;
    }
    
    int update_specific_apps() {
        std::cout << "Updating specific apps: ";
        for (size_t i = 0; i < apps_.size(); ++i) {
            std::cout << apps_[i];
            if (i < apps_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";
        
        // Check which apps are installed
        auto installed_apps = get_installed_apps();
        std::vector<std::string> apps_to_update;
        std::vector<std::string> not_installed;
        
        for (const auto& app : apps_) {
            if (std::find(installed_apps.begin(), installed_apps.end(), app) != installed_apps.end()) {
                apps_to_update.push_back(app);
            } else {
                not_installed.push_back(app);
            }
        }
        
        // Report apps that are not installed
        if (!not_installed.empty()) {
            std::cout << "The following apps are not installed:\n";
            for (const auto& app : not_installed) {
                std::cout << "  - " << app << "\n";
            }
            std::cout << "\n";
        }
        
        if (apps_to_update.empty()) {
            std::cout << "No specified apps are currently installed.\n";
            return 1;
        }
        
        // Check for updates and show status for each app
        auto updates_available = check_for_updates(apps_to_update);

        // Show status for apps that don't need updates
        show_app_status(apps_to_update, updates_available);

        if (updates_available.empty()) {
            return 0;
        }
        
        // Show what will be updated
        show_update_summary(updates_available);
        
        // Perform updates
        UpdateManager::UpdateOptions options;
        options.global = global_;
        options.force = force_;
        options.no_cache = no_cache_;
        options.skip_dependencies = skip_dependencies_;
        
        auto start_time = std::chrono::steady_clock::now();
        auto result = UpdateManager::update_apps(updates_available, options);
        auto end_time = std::chrono::steady_clock::now();
        
        show_update_results(result, start_time, end_time);
        
        return result.success ? 0 : 1;
    }
    
    int update_scoop_itself() {
        std::cout << "Updating Scoop itself...\n";
        
        // Check if we're running the C++ version or PowerShell version
        std::cout << "Note: This is the C++ implementation of Scoop (sco).\n";
        std::cout << "To update sco, please use your package manager or download the latest release.\n";
        
        // Update buckets instead
        std::cout << "\nUpdating buckets...\n";
        auto result = update_buckets();
        
        if (result) {
            std::cout << "Buckets updated successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to update some buckets.\n";
            return 1;
        }
    }
    
    std::vector<std::string> get_installed_apps() {
        std::vector<std::string> apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    // Skip special directories
                    if (app_name != "scoop" && app_name != ".git") {
                        apps.push_back(app_name);
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error reading apps directory: {}", e.what());
        }
        
        return apps;
    }
    
    std::vector<UpdateManager::UpdateInfo> check_for_updates(const std::vector<std::string>& apps) {
        std::vector<UpdateManager::UpdateInfo> updates;
        
        std::cout << "Checking for updates...\n";
        
        for (const auto& app : apps) {
            UpdateManager::UpdateInfo info;
            info.name = app;
            info.current_version = get_installed_version(app);

            // Get latest version from manifest
            auto manifest_info = get_latest_manifest_info(app);
            info.latest_version = manifest_info.first;
            info.bucket = manifest_info.second;

            // Only add to updates if we have valid versions and the latest is actually newer
            if (!info.latest_version.empty() && !info.current_version.empty() &&
                info.current_version != "unknown" && needs_update(info.current_version, info.latest_version)) {
                updates.push_back(info);
            }
        }
        
        return updates;
    }
    
    std::string get_installed_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;

        if (!std::filesystem::exists(app_dir)) {
            return "";
        }

        // Use the same logic as list command for consistency
        // Step 1: Try to read from current\manifest.json first (Scoop's primary method)
        auto current_manifest = app_dir / "current" / "manifest.json";
        if (std::filesystem::exists(current_manifest)) {
            try {
                std::ifstream file(current_manifest);
                if (file.is_open()) {
                    nlohmann::json manifest_info;
                    file >> manifest_info;
                    if (manifest_info.contains("version")) {
                        std::string version = manifest_info["version"].get<std::string>();
                        // Handle nightly versions - get version from directory name
                        if (version == "nightly") {
                            auto current_dir = app_dir / "current";
                            if (std::filesystem::is_symlink(current_dir)) {
                                try {
                                    auto target = std::filesystem::read_symlink(current_dir);
                                    return target.filename().string();
                                } catch (const std::exception&) {
                                    // Fall through to other methods
                                }
                            }
                        } else {
                            return version;
                        }
                    }
                }
            } catch (const std::exception&) {
                // Ignore JSON parsing errors
            }
        }

        // Step 2: Try to read from Scoop's install.json (for compatibility)
        auto install_json = app_dir / "current" / "install.json";
        if (std::filesystem::exists(install_json)) {
            try {
                std::ifstream file(install_json);
                if (file.is_open()) {
                    nlohmann::json install_info;
                    file >> install_info;
                    if (install_info.contains("version")) {
                        return install_info["version"].get<std::string>();
                    }
                }
            } catch (const std::exception&) {
                // Ignore JSON parsing errors
            }
        }

        // Step 3: Try to read from SCO's scoop-install.json (fallback)
        auto scoop_install = app_dir / "current" / "scoop-install.json";
        if (std::filesystem::exists(scoop_install)) {
            try {
                std::ifstream file(scoop_install);
                if (file.is_open()) {
                    nlohmann::json install_info;
                    file >> install_info;
                    if (install_info.contains("version")) {
                        return install_info["version"].get<std::string>();
                    }
                }
            } catch (const std::exception&) {
                // Ignore JSON parsing errors
            }
        }

        // Step 4: Look for current.txt as fallback
        auto current_file = app_dir / "current.txt";
        if (std::filesystem::exists(current_file)) {
            std::ifstream file(current_file);
            if (file.is_open()) {
                std::string version;
                std::getline(file, version);
                return version.empty() ? "unknown" : version;
            }
        }

        // Step 5: Check for version directories as last resort (like list command)
        try {
            for (const auto& version_entry : std::filesystem::directory_iterator(app_dir)) {
                if (version_entry.is_directory()) {
                    std::string dir_name = version_entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "persist") {
                        return dir_name;
                    }
                }
            }
        } catch (const std::exception&) {
            // Ignore errors
        }

        return "unknown";
    }

    std::vector<std::string> get_all_installed_versions(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        std::vector<std::string> versions;

        if (!std::filesystem::exists(app_dir)) {
            return versions;
        }

        try {
            // Look for version directories that contain install.json
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();

                    // Skip 'current' directory and backup directories
                    if (dir_name == "current" || dir_name.starts_with("_") || dir_name.find(".old") != std::string::npos) {
                        continue;
                    }

                    // Check if this version directory has install.json
                    auto install_json = entry.path() / "install.json";
                    if (std::filesystem::exists(install_json)) {
                        versions.push_back(dir_name);
                    }
                }
            }

            // Sort versions (simple string sort for now, could be improved with proper version comparison)
            std::sort(versions.begin(), versions.end());

        } catch (const std::exception& e) {
            output::debug("Failed to get installed versions for {}: {}", app_name, e.what());
        }

        return versions;
    }

    bool needs_update(const std::string& current_version, const std::string& latest_version) {
        // If versions are exactly the same, no update needed
        if (current_version == latest_version) {
            return false;
        }

        // Handle special cases
        if (current_version == "unknown" || latest_version.empty()) {
            return false;
        }

        // For now, use simple string comparison
        // This could be improved with proper semantic version comparison
        // following Scoop's Compare-Version logic
        return current_version != latest_version;
    }

    std::pair<std::string, std::string> get_latest_manifest_info(const std::string& app_name) {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            return {"", ""};
        }
        
        // Search through all buckets for the app manifest
        try {
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    std::string bucket_name = bucket_entry.path().filename().string();
                    auto manifest_path = bucket_entry.path() / "bucket" / (app_name + ".json");
                    
                    if (std::filesystem::exists(manifest_path)) {
                        try {
                            std::ifstream file(manifest_path);
                            nlohmann::json manifest;
                            file >> manifest;
                            
                            if (manifest.contains("version")) {
                                return {manifest["version"].get<std::string>(), bucket_name};
                            }
                        } catch (const std::exception& e) {
                            output::debug("Failed to read manifest for {}: {}", app_name, e.what());
                        }
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error searching for manifest: {}", e.what());
        }
        
        return {"", ""};
    }

    void show_app_status(const std::vector<std::string>& requested_apps,
                        const std::vector<UpdateManager::UpdateInfo>& updates_available) {
        // Create a set of apps that have updates available
        std::set<std::string> apps_with_updates;
        for (const auto& update : updates_available) {
            apps_with_updates.insert(update.name);
        }

        // Show status for each requested app
        for (const auto& app : requested_apps) {
            if (apps_with_updates.find(app) == apps_with_updates.end()) {
                // App doesn't need update - show current version
                std::string current_version = get_installed_version(app);
                auto manifest_info = get_latest_manifest_info(app);
                std::string latest_version = manifest_info.first;

                if (current_version.empty() || current_version == "unknown") {
                    std::cout << app << ": not installed\n";
                } else if (latest_version.empty()) {
                    std::cout << app << ": " << current_version << " (manifest not found)\n";
                } else {
                    std::cout << app << ": " << current_version << " (latest version)\n";
                }
            }
        }
    }

    void show_update_summary(const std::vector<UpdateManager::UpdateInfo>& updates) {
        std::cout << "The following apps will be updated:\n\n";
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Current", true);
        table.add_column("Latest", true);
        table.add_column("Source", true);
        
        for (const auto& update : updates) {
            table.add_row({
                update.name,
                update.current_version,
                update.latest_version,
                update.bucket
            });
        }
        
        table.print();
        std::cout << "\n";
    }
    
    void show_update_results(const UpdateManager::UpdateResult& result,
                           const std::chrono::steady_clock::time_point& start_time,
                           const std::chrono::steady_clock::time_point& end_time) {
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
        
        std::cout << "\nUpdate completed in " << duration.count() << " seconds.\n";
        
        if (!result.updated_apps.empty()) {
            std::cout << "\nSuccessfully updated:\n";
            for (const auto& app : result.updated_apps) {
                output::success("  " + app);
            }
        }
        
        if (!result.failed_apps.empty()) {
            std::cout << "\nFailed to update:\n";
            for (const auto& app : result.failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
        
        if (!result.skipped_apps.empty()) {
            std::cout << "\nSkipped (already up to date):\n";
            for (const auto& app : result.skipped_apps) {
                std::cout << "  - " << app << "\n";
            }
        }
    }
    
    bool update_buckets() {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            std::cout << "No buckets directory found.\n";
            return true; // Not an error if no buckets exist
        }
        
        bool all_success = true;
        
        try {
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    std::string bucket_name = bucket_entry.path().filename().string();
                    std::cout << "Updating bucket: " << bucket_name << "... ";
                    
                    // Check if it's a git repository
                    auto git_dir = bucket_entry.path() / ".git";
                    if (std::filesystem::exists(git_dir)) {
                        // Try to update using git
                        std::string command = "git -C \"" + bucket_entry.path().string() + "\" pull";
                        int result = std::system(command.c_str());
                        
                        if (result == 0) {
                            std::cout << "OK\n";
                        } else {
                            std::cout << "Failed\n";
                            all_success = false;
                        }
                    } else {
                        std::cout << "Not a git repository, skipping\n";
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error updating buckets: {}", e.what());
            all_success = false;
        }
        
        return all_success;
    }
};

} // namespace sco
