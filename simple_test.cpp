#include "src/utils/shim_manager.hpp"
#include "src/core/config.hpp"
#include "src/utils/output.hpp"
#include <iostream>

int main() {
    try {
        // Initialize output system
        sco::output::init(true, false); // verbose=true, quiet=false
        
        // Load config
        auto& config = sco::Config::instance();
        config.load();
        
        std::cout << "=== SCO PATH Test ===" << std::endl;
        std::cout << "Shims directory: " << config.get_shims_dir().string() << std::endl;
        
        // Check if shims directory is in user registry PATH
        bool in_path_before = sco::ShimManager::is_shims_in_path();
        std::cout << "Shims in user registry PATH before: " << (in_path_before ? "YES" : "NO") << std::endl;
        
        // Try to add shims to PATH
        std::cout << "\nAttempting to add shims directory to user PATH..." << std::endl;
        bool added = sco::ShimManager::add_shims_to_path();
        std::cout << "Add operation result: " << (added ? "SUCCESS" : "FAILED") << std::endl;
        
        // Check again
        bool in_path_after = sco::ShimManager::is_shims_in_path();
        std::cout << "Shims in user registry PATH after: " << (in_path_after ? "YES" : "NO") << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
