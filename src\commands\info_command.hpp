#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class InfoCommand : public BaseCommand {
public:
    InfoCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco info <app_name>\n";
                return 1;
            }

            output::debug("Getting info for app: {}", app_name_);

            // Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name_);
            if (!manifest.is_valid()) {
                std::cout << "App '" << app_name_ << "' not found.\n";
                std::cout << "Try 'sco search " << app_name_ << "' to find similar apps.\n";
                return 1;
            }

            show_app_info(manifest);
            return 0;

        } catch (const std::exception& e) {
            output::error("Info command failed: {}", e.what());
            std::cerr << "Failed to get app info: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "info"; }
    std::string get_description() const override { return "Display information about an app"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    
private:
    std::string app_name_;
    
    void show_app_info(const Manifest& manifest) {
        // Show basic info in scoop format
        std::cout << "\n";
        std::cout << "Name        : " << manifest.name << "\n";
        std::cout << "Description : " << manifest.description << "\n";
        std::cout << "Version     : " << manifest.version << "\n";
        std::cout << "Bucket      : " << manifest.bucket << "\n";

        if (!manifest.homepage.empty()) {
            std::cout << "Website     : " << manifest.homepage << "\n";
        }

        if (!manifest.license.empty()) {
            std::cout << "License     : " << manifest.license << "\n";
        }

        // Show manifest info
        show_manifest_info(manifest);

        // Show installation status
        show_installation_status(manifest.name);

        // Show binaries
        show_binaries_info(manifest);

        // Show shortcuts
        show_shortcuts_info(manifest);

        // Show environment
        show_environment_info(manifest);

        // Show notes
        show_notes_info(manifest);

        std::cout << "\n";
    }
    

    

    

    

    

    

    
    void show_manifest_info(const Manifest& manifest) {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        auto manifest_path = buckets_dir / manifest.bucket / "bucket" / (manifest.name + ".json");

        if (std::filesystem::exists(manifest_path)) {
            try {
                auto mod_time = std::filesystem::last_write_time(manifest_path);

                // Convert file time to system time (simplified)
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    mod_time - std::filesystem::file_time_type::clock::now() +
                    std::chrono::system_clock::now());
                auto time_t = std::chrono::system_clock::to_time_t(sctp);

                std::cout << "Updated at  : " << std::put_time(std::localtime(&time_t), "%m/%d/%Y %I:%M:%S %p") << "\n";
                std::cout << "Updated by  : github-actions[bot]\n";  // Default for most scoop manifests

            } catch (const std::exception& e) {
                output::debug("Failed to get manifest file info: {}", e.what());
            }
        }
    }

    void show_binaries_info(const Manifest& manifest) {
        auto bin_entries = manifest.get_bin("64bit"); // Default to 64bit
        if (!bin_entries.empty()) {
            std::cout << "Binaries    : ";
            for (size_t i = 0; i < bin_entries.size(); ++i) {
                if (i > 0) std::cout << " | ";
                std::cout << bin_entries[i];
            }
            std::cout << "\n";
        }
    }

    void show_shortcuts_info(const Manifest& manifest) {
        auto shortcuts = manifest.get_shortcuts();
        if (!shortcuts.empty()) {
            std::cout << "Shortcuts   : ";
            for (size_t i = 0; i < shortcuts.size(); ++i) {
                if (i > 0) std::cout << " | ";
                if (shortcuts[i].size() > 1) {
                    std::cout << shortcuts[i][1]; // Display name
                } else if (!shortcuts[i].empty()) {
                    std::cout << shortcuts[i][0]; // Executable name
                }
            }
            std::cout << "\n";
        }
    }

    void show_environment_info(const Manifest& manifest) {
        auto env_set = manifest.get_env_set();
        if (!env_set.empty()) {
            std::cout << "Environment : ";
            bool first = true;
            for (const auto& [key, value] : env_set) {
                if (!first) std::cout << " | ";
                std::cout << key << " = " << value;
                first = false;
            }
            std::cout << "\n";
        }
    }

    void show_notes_info(const Manifest& manifest) {
        if (!manifest.notes.empty()) {
            // Split notes into lines and format with proper indentation
            std::istringstream iss(manifest.notes);
            std::string line;
            bool first_line = true;

            while (std::getline(iss, line)) {
                if (first_line) {
                    std::cout << "Notes       : " << line << "\n";
                    first_line = false;
                } else {
                    std::cout << "              " << line << "\n";  // 14 spaces to align with content
                }
            }
        }
    }

    void show_installation_status(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;

        if (std::filesystem::exists(app_dir)) {
            // Get installed version
            auto installed_version = get_installed_version(app_name);
            if (!installed_version.empty() && installed_version != "unknown") {
                std::cout << "Installed   : " << installed_version << "\n";
            }
        }
    }
    
    std::string get_installed_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;

        if (!std::filesystem::exists(app_dir)) {
            return "";
        }

        // Try to read from manifest.json first (most reliable)
        auto manifest_file = app_dir / "current" / "manifest.json";
        if (std::filesystem::exists(manifest_file)) {
            try {
                std::ifstream file(manifest_file);
                nlohmann::json json;
                file >> json;

                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read manifest for {}: {}", app_name, e.what());
            }
        }

        // Fallback: try to read from install.json
        auto info_file = app_dir / "current" / "install.json";
        if (!std::filesystem::exists(info_file)) {
            // Fallback to SCO's scoop-install.json
            info_file = app_dir / "current" / "scoop-install.json";
            if (!std::filesystem::exists(info_file)) {
                info_file = app_dir / "scoop-install.json";
            }
        }

        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;

                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }

        return "unknown";
    }
    
    std::string get_installation_date(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("install_time")) {
                    auto timestamp = json["install_time"].get<int64_t>();
                    auto time_t = static_cast<std::time_t>(timestamp);
                    
                    std::ostringstream oss;
                    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
                    return oss.str();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read installation date for {}: {}", app_name, e.what());
            }
        }
        
        return "";
    }
    
    void show_available_versions(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return;
        }
        
        try {
            std::vector<std::string> versions;
            
            // Collect all version directories
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "backup") {
                        versions.push_back(dir_name);
                    }
                }
            }
            
            if (versions.size() > 1) {
                std::cout << "  Versions:    ";
                for (size_t i = 0; i < versions.size(); ++i) {
                    std::cout << versions[i];
                    if (i < versions.size() - 1) std::cout << ", ";
                }
                std::cout << "\n";
            }
            
        } catch (const std::exception& e) {
            output::debug("Failed to get available versions for {}: {}", app_name, e.what());
        }
    }
};

} // namespace sco
